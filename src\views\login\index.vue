<script setup lang="ts">
import LogoDark from "@/assets/images/logo_dark.png"
import LogoLight from "@/assets/images/logo_light.png"
import { Login, ResetPwd } from "./components"

// type IformType = "login" | "register" | "resetPwd"
type IformType = "login" | "resetPwd"
const formType: Ref<IformType> = ref("login")
const formComponets = {
  login: Login,
  // register: Register,
  resetPwd: ResetPwd,
}

const appName = import.meta.env.VITE_APP_NAME

const isDark = useDark()
</script>

<template>
  <n-el :class="isDark ? '' : 'bg-[url(@/assets/images/login_bg.webp)] bg-cover'" class="flex h-full w-full items-center justify-center" style="background-color: var(--body-color);">
    <div class="text-lg right-40px top-40px fixed">
      <DarkModeSwitch />
      <LangsSwitch />
    </div>
    <n-el
      class="p-4xl h-full w-full sm:h-unset sm:w-450px"
      style="background: var(--card-color);box-shadow: var(--box-shadow-1);"
    >
      <div class="flex flex-col w-full items-center">
        <img :src="isDark ? LogoDark : LogoLight" class="size-6em" alt="logo">
        <n-h3>{{ appName }} </n-h3>
        <transition
          name="fade-slide"
          mode="out-in"
        >
          <component
            :is="formComponets[formType]"
            v-model="formType"
            class="w-85%"
          />
        </transition>
      </div>
    </n-el>

    <div />
  </n-el>
</template>
