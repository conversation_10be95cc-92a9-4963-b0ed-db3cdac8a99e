import { useRsa } from "@/hooks/useRsa"
import { request } from "../http"
// import { DataEncodeFn, GenerateKeyFn, Random16CharsFn } from "../rsa/rsaMethod"

interface Ilogin {
  account: string
  password: string
  key: string
  captcha: string
}

export function fetchUpdateToken(data: any) {
  const method = request.Post<Service.ResponseResult<Api.Login.Info>>("/updateToken", data)
  method.meta = {
    authRole: "refreshToken",
  }
  return method
}

export function fetchUserRoutes(params: { id: number }) {
  return request.Get<Service.ResponseResult<AppRoute.RowRoute[]>>("/getUserRoutes", { params })
}

// 验证码
export async function adminCaptcha() {
  const { data, key16 } = await useRsa({})
  return request.Post<Service.ResponseResult<Api.Login.ICaptcha>>("/admin/captcha", data, { meta: { key16 } })
}

// 登录
export async function fetchLogin(rawData: Ilogin) {
  const { data, key16 } = await useRsa(rawData)

  return request.Post<Service.ResponseResult<Api.Login.Info>>("/admin/login", data, { meta: { key16 } })
}

// 退出登录
export async function adminLogout() {
  const { data, key16 } = await useRsa({})
  return request.Post("/admin/logout", data, { meta: { key16 } })
}
