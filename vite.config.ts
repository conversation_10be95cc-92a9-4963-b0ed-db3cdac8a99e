import type { ConfigEnv, UserConfig } from "vite"
import { dirname, resolve } from "node:path"
import { fileURLToPath, URL } from "node:url"
import VueI18nPlugin from "@intlify/unplugin-vue-i18n/vite"
import BuildInfoVitePlugin from "@renzp/unplugin-build-info/vite"
import vue from "@vitejs/plugin-vue"
import vueJsx from "@vitejs/plugin-vue-jsx"
import UnoCSS from "unocss/vite"
import AutoImport from "unplugin-auto-import/vite"
import { FileSystemIconLoader } from "unplugin-icons/loaders"
import IconsResolver from "unplugin-icons/resolver"
import Icons from "unplugin-icons/vite"
import TurboConsole from "unplugin-turbo-console/vite"
import { NaiveUiResolver, VueUseComponentsResolver, VueUseDirectiveResolver } from "unplugin-vue-components/resolvers"
import Components from "unplugin-vue-components/vite"
import { defineConfig, loadEnv } from "vite"
import checker from "vite-plugin-checker"
import { envParse, parseLoadedEnv } from "vite-plugin-env-parse"
import fullReload from "vite-plugin-full-reload"
import removeConsole from "vite-plugin-remove-console"
import requireTransform from "vite-plugin-require-transform"

export default defineConfig(({ mode }: ConfigEnv): UserConfig => {
  const viteEnv = parseLoadedEnv(loadEnv(mode, process.cwd())) as ImportMetaEnv
  // const isProd = mode !== "development"
  return {
    base: viteEnv.VITE_BASE_URL,
    plugins: [
      vue(),
      vueJsx(),
      UnoCSS({
        inspector: false,
      }),
      AutoImport({
      // dirs: ["src/hooks"],
        imports: [
          "vue",
          "pinia",
          "vue-router",
          "@vueuse/core",
          "vue-i18n",
          {
            "naive-ui": [
              "useDialog",
              "useMessage",
              "useNotification",
              "useLoadingBar",
              "useModal",
            ],
          },
        ],
        dts: "src/typings/auto-imports.d.ts",
      }),
      Components({
        dts: "src/typings/components.d.ts",
        resolvers: [IconsResolver({
          prefix: false,
          customCollections: [
            "svg-icons",
          ],
        }), NaiveUiResolver(), VueUseComponentsResolver(), VueUseDirectiveResolver()],
      }),
      VueI18nPlugin({
        include: resolve(dirname(fileURLToPath(import.meta.url)), "./locales/**"),
      }),
      Icons({
        defaultStyle: "display:inline-block",
        compiler: "vue3",
        customCollections: {
          "svg-icons": FileSystemIconLoader(
            "src/assets/svg-icons",
            svg => svg.replace(/^<svg /, "<svg fill=\"currentColor\" width=\"1.2em\" height=\"1.2em\""),
          ),
        },
      }),
      fullReload(["src/**/*"]),
      envParse({
        build: true,
        dev: true,
        dtsPath: "src/typings/env-parse.d.ts",
        parseJson: true,
      }),
      BuildInfoVitePlugin(),
      TurboConsole({
        inspector: false,
        launchEditor: false,
        passLogs: false,
      }),
      requireTransform({ fileRegex: /.ts$|.tsx$|.vue$/ }),
      checker({ typescript: true, vueTsc: true, overlay: false }),
      viteEnv.VITE_DEL_CONSOLE && removeConsole({ includes: ["log", "warn", "error", "info"], externalValue: ["这个不删", "noRemove"] }),
    ],
    preview: {
      host: true,
      open: false,
      port: 80,
      strictPort: false,
    },
    server: {
      cors: true,
      hmr: true,
      host: true,
      open: false,
      port: 8080,
      proxy: {
        "/PROXY": {
          changeOrigin: true,
          rewrite: url => url.replace(/^\/PROXY/, ""),
          target: "https://xcyp-dev01.sandpay.com.cn/merapi",
        },
      },
      strictPort: false,
    },
    resolve: {
      alias: {
        "@": fileURLToPath(new URL("./src", import.meta.url)),
      },
    },
    optimizeDeps: {
      include: ["echarts"],
    },
    build: {
      // assetsDir: "assets",
      // assetsInlineLimit: 4 * 1024,
      // chunkSizeWarningLimit: 1024,
      // reportCompressedSize: false,
      // rolldownOptions: {
      //   output: {
      //     assetFileNames: "assets/[ext]/[name]-[hash].[ext]",
      //     chunkFileNames: "assets/js/[name]-[hash].js",
      //     entryFileNames: "assets/js/[name]-[hash].js",
      //     manualChunks(id) {
      //       if (id.includes("node_modules"))
      //         return "vendor"
      //     },
      //   },
      // },
      // sourcemap: false,

      // 基础构建优化
      minify: "oxc", // Vite 7 默认，比 terser 更快
      target: "baseline-widely-available", // Vite 7 新默认值，更好的浏览器兼容性
      chunkSizeWarningLimit: 800, // 适度调高，减少无意义警告

      rolldownOptions: {
        output: {
          manualChunks(id) {
            if (
              [
                "vue",
                "vue-router",
                "pinia",
                "pinia-plugin-persistedstate",
              ].some(pkg => id.includes(`/node_modules/${pkg}/`))
            ) {
              return "vue-vendor"
            }
            if (id.includes("/node_modules/naive-ui/")) {
              return "ui-vendor"
            }
            if (
              [
                "echarts",
                "@antv/x6",
                "@vue-flow/core",
                "@visactor/vtable-gantt",
              ].some(pkg => id.includes(`/node_modules/${pkg}/`))
            ) {
              return "charts-vendor"
            }
            if (
              [
                "@kangc/v-md-editor",
                "wangeditor",
                "highlight.js",
              ].some(pkg => id.includes(`/node_modules/${pkg}/`))
            ) {
              return "editor-vendor"
            }
            if (
              [
                "xlsx",
                "@tato30/vue-pdf",
                "mammoth",
                "file-saver",
                "jszip",
                "jszip-utils",
              ].some(pkg => id.includes(`/node_modules/${pkg}/`))
            ) {
              return "office-vendor"
            }
            if (
              [
                "@fullcalendar/core",
                "@fullcalendar/daygrid",
                "@fullcalendar/interaction",
                "@fullcalendar/list",
                "@fullcalendar/vue3",
              ].some(pkg => id.includes(`/node_modules/${pkg}/`))
            ) {
              return "calendar-vendor"
            }
            if (id.includes("/node_modules/@splinetool/runtime/")) {
              return "spline-vendor"
            }
          },

          // 优化文件组织结构
          chunkFileNames: "js/[name]-[hash].js",
          entryFileNames: "js/[name]-[hash].js",
          assetFileNames: (assetInfo) => {
            const name = assetInfo.name || ""

            // 按文件类型分目录
            if (/\.(?:png|jpe?g|gif|svg|webp|avif)$/i.test(name)) {
              return "images/[name]-[hash].[ext]"
            }
            if (/\.(?:woff2?|eot|ttf|otf)$/i.test(name)) {
              return "fonts/[name]-[hash].[ext]"
            }
            if (/\.(?:mp4|webm|ogg|mp3|wav|flac|aac)$/i.test(name)) {
              return "media/[name]-[hash].[ext]"
            }

            return "assets/[name]-[hash].[ext]"
          },
        },
      },

      // 现代构建优化
      cssCodeSplit: true, // 启用 CSS 代码分割
      sourcemap: false, // 生产环境关闭 sourcemap
      reportCompressedSize: false, // 关闭压缩大小报告，加快构建
    },
  }
})
