{
  "compilerOptions": {
    "target": "ESNext",
    "jsx": "preserve",
    "jsxImportSource": "vue",
    "lib": ["ESNext", "DOM"],
    "baseUrl": ".",
    "module": "ESNext",
    "moduleResolution": "bundler",
    "paths": {
      // "~/*": ["./*"],
      "@/*": ["src/*"]
    },
    "resolveJsonModule": true,
    "types": ["node", "naive-ui/volar", "unplugin-icons/types/vue"],
    "allowJs": true,
    "strict": true,
    "strictNullChecks": true,
    "noUnusedLocals": true,
    "outDir": "./dist",
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "isolatedModules": true,
    "skipLibCheck": true
  },
  "exclude": ["node_modules", "dist"]
}
