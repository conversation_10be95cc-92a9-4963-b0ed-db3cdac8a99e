import { router } from "@/router"
import { adminLogout, fetchLogin } from "@/service"
import { local } from "@/utils"
import { useRouteStore } from "./router"
import { useTabStore } from "./tab"

interface AuthStatus {
  userInfo: Api.Login.Info | null
  token: string
}

export const useAuthStore = defineStore("auth-store", {
  state: (): AuthStatus => {
    return {
      userInfo: local.get("userInfo"),
      token: local.get("token") || "",
    }
  },
  getters: {
    /** 是否登录 */
    isLogin(state) {
      return Boolean(state.token)
    },
  },
  actions: {
    /* 登录退出，重置用户信息等 */
    async logout() {
      // 退出接口
      // await adminLogout()

      const route = unref(router.currentRoute)
      // 清除本地缓存
      this.clearAuthStorage()
      // 清空路由、菜单等数据
      const routeStore = useRouteStore()
      routeStore.resetRouteStore()
      // 清空标签栏数据
      const tabStore = useTabStore()
      tabStore.clearAllTabs()
      // 重置当前存储库
      this.$reset()
      // 重定向到登录页
      if (route.meta.requiresAuth) {
        router.push({
          name: "login",
          query: {
            redirect: route.fullPath,
          },
        })
      }
    },
    clearAuthStorage() {
      local.remove("token")
      local.remove("userInfo")
    },
    /* 用户登录 */
    async login(rowData: { account: string, password: string, captcha: string, key: string }, fb: () => void) {
      try {
        const { isSuccess, data } = await fetchLogin(rowData)
        console.log("data", isSuccess, data)
        if (!isSuccess)
          return fb()

        // 处理登录信息
        await this.handleLoginInfo(data)
      }
      catch (e) {
        console.warn("[Login Error]:", e)
      }
    },

    /* 处理登录返回的数据 */
    async handleLoginInfo(data: Api.Login.Info) {
      // 将token和userInfo保存下来
      local.set("userInfo", data, data.expires)
      local.set("token", data.token, data.expires)
      this.token = data.token
      this.userInfo = data

      // 添加路由和菜单
      const routeStore = useRouteStore()
      await routeStore.initAuthRoute()

      // 进行重定向跳转
      const route = unref(router.currentRoute)
      const query = route.query as { redirect: string }
      router.push({
        path: query.redirect || "/",
      })
    },
  },
})
