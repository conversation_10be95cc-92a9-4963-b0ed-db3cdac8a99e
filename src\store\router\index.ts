import type { MenuOption } from "naive-ui"
import { router } from "@/router"
import { useAuthStore } from "@/store/auth"
import { $t, local } from "@/utils"
import { createMenus, createRoutes, generateCacheRoutes } from "./helper"

interface RoutesStatus {
  isInitAuthRoute: boolean
  menus: MenuOption[]
  rowRoutes: AppRoute.RowRoute[]
  activeMenu: string | null
  cacheRoutes: string[]
}
export const useRouteStore = defineStore("route-store", {
  state: (): RoutesStatus => {
    return {
      isInitAuthRoute: false,
      activeMenu: null,
      menus: [],
      rowRoutes: [],
      cacheRoutes: [],
    }
  },
  actions: {
    resetRouteStore() {
      this.resetRoutes()
      this.$reset()
    },
    resetRoutes() {
      if (router.hasRoute("appRoot"))
        router.removeRoute("appRoot")
    },
    // set the currently highlighted menu key
    setActiveMenu(key: string) {
      this.activeMenu = key
    },

    async initRouteInfo() {
      const userInfo = local.get("userInfo")
      const authStore = useAuthStore()
      if (!userInfo) {
        authStore.logout()
        return
      }
      return authStore.userInfo?.power_list
      // return authStore.userInfo?.power_list_two
    },
    async initAuthRoute() {
      this.isInitAuthRoute = false

      // Initialize route information
      const rowRoutes = await this.initRouteInfo()
      console.log("rowRoutes", rowRoutes)
      if (!rowRoutes) {
        window.$message.error($t(`app.getRouteError`))
        return
      }
      this.rowRoutes = rowRoutes

      // console.log(1)

      // Generate actual route and insert
      const routes = createRoutes(rowRoutes)
      // console.log(2)

      // console.log("routes", routes)

      // console.log("router", router)
      router.addRoute(routes)
      console.log("router", router)
      // console.log(3)

      // Generate side menu
      this.menus = createMenus(rowRoutes)
      // console.log(4)

      // Generate the route cache
      this.cacheRoutes = generateCacheRoutes(rowRoutes)
      // console.log(5)

      this.isInitAuthRoute = true
    },
  },
})
