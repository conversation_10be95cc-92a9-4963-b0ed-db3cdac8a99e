{"name": "admin", "type": "module", "version": "0.0.0", "private": true, "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite --debug proxy", "build": "run-p type-check \"build-only {@}\" --", "build:test": "run-p type-check \"build-only-test {@}\" --", "preview": "vite preview", "build-only": "vite build", "build-only-test": "vite build --mode test", "type-check": "vue-tsc --build", "lint": "eslint", "lint:fix": "eslint --fix"}, "dependencies": {"@iconify/vue": "^5.0.0", "@vueuse/components": "^13.6.0", "@vueuse/core": "^13.6.0", "alova": "^3.3.4", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "echarts": "^6.0.0", "es-toolkit": "^1.39.9", "jsencrypt": "^3.5.4", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "pinia": "^3.0.3", "pro-naive-ui": "^3.0.0", "radash": "^12.1.1", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@antfu/eslint-config": "^5.2.1", "@iconify/json": "^2.2.371", "@intlify/unplugin-vue-i18n": "^6.0.8", "@renzp/unplugin-build-info": "^1.0.3", "@tsconfig/node22": "^22.0.2", "@types/crypto-js": "^4.2.2", "@types/node": "^24.2.1", "@unocss/eslint-plugin": "^66.4.2", "@unocss/reset": "^66.4.2", "@vitejs/plugin-vue": "^6.0.1", "@vitejs/plugin-vue-jsx": "^5.0.1", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "browserslist": "^4.25.2", "colord": "^2.9.3", "eslint": "^9.33.0", "eslint-plugin-format": "^1.0.1", "lightningcss": "^1.30.1", "localforage": "^1.10.0", "naive-ui": "^2.42.0", "npm-run-all2": "^8.0.4", "pinia-plugin-persistedstate": "^4.5.0", "typescript": "^5.9.2", "unocss": "^66.4.2", "unocss-preset-autoprefixer": "^0.0.9", "unplugin-auto-import": "^20.0.0", "unplugin-icons": "^22.2.0", "unplugin-turbo-console": "^2.2.0", "unplugin-vue-components": "^29.0.0", "vite": "npm:rolldown-vite@latest", "vite-plugin-checker": "^0.10.2", "vite-plugin-env-parse": "^1.0.15", "vite-plugin-full-reload": "^1.2.0", "vite-plugin-remove-console": "^2.2.0", "vite-plugin-require-transform": "^1.0.21", "vite-plugin-vue-devtools": "^8.0.0", "vue-draggable-plus": "^0.6.0", "vue-i18n": "^11.1.11", "vue-tsc": "^3.0.5"}}