import type { MenuOption } from "naive-ui"
import type { RouteRecordRaw } from "vue-router"
import { clone, omit, pick } from "es-toolkit"
import { minBy } from "es-toolkit/compat"
import { RouterLink } from "vue-router"
import { usePermission } from "@/hooks"
import Layout from "@/layouts/index.vue"
import { $t, arrayToTree, renderIcon } from "@/utils"

const metaFields: AppRoute.MetaKeys[]
  = ["title", "icon", "requiresAuth", "roles", "keepAlive", "hide", "order", "href", "activeMenu", "withoutTab", "pinTab", "menuType"]

function standardizedRoutes(route: AppRoute.RowRoute[]) {
  return clone(route).map((i) => {
    const route = omit(i, metaFields)

    Reflect.set(route, "meta", pick(i, metaFields))
    return route
  }) as AppRoute.Route[]
}

export function createRoutes(routes: AppRoute.RowRoute[]) {
  // Generate routes, no need to import files for those with redirect
  const modules = import.meta.glob("@/views/**/*.vue")
  const resultRouter = routes.map((item: AppRoute.Route) => {
    if (item.path)
      item.component = modules[`/src/views${item.path}.vue`]
    return item
  })

  console.log("resultRouter", resultRouter)

  // Generate route tree
  // resultRouter = arrayToTree(resultRouter) as AppRoute.Route[]
  console.log("resultRouter", resultRouter)
  const appRootRoute: RouteRecordRaw = {
    path: "/appRoot",
    name: "appRoot",
    redirect: import.meta.env.VITE_HOME_PATH,
    component: Layout,
    meta: {
      title: "",
      icon: "icon-park-outline:home",
    },
    children: [],
  }

  // Set the correct redirect path for the route
  // setRedirect(resultRouter)

  // Insert the processed route into the root route
  appRootRoute.children = resultRouter as unknown as RouteRecordRaw[]
  return appRootRoute
}

// Generate an array of route names that need to be kept alive
export function generateCacheRoutes(routes: AppRoute.RowRoute[]) {
  return routes
    .filter(i => i.keepAlive)
    .map(i => i.name)
}

/* 生成侧边菜单的数据 */
export function createMenus(userRoutes: AppRoute.RowRoute[]) {
  const resultMenus = standardizedRoutes(userRoutes)

  console.log("resultMenus", resultMenus)

  // filter menus that do not need to be displayed
  const visibleMenus = resultMenus.filter(route => !route.meta.hide)

  // generate side menu
  // return arrayToTree(transformAuthRoutesToMenus(visibleMenus))
  return transformAuthRoutesToMenus(visibleMenus)
}

// render the returned routing table as a sidebar
function transformAuthRoutesToMenus(userRoutes: AppRoute.Route[]) {
  return userRoutes
    // Convert to side menu data structure
    .map((item) => {
      console.log("item", item)
      const target: MenuOption = {
        id: item.id,
        pid: item.pid,
        child: item.child,
        label: () => $t(`route.${String(item.name)}`, item.meta.title),
        key: item.path,
        icon: item.meta.icon ? renderIcon(item.meta.icon) : undefined,
      }
      console.log("target", target)
      return target
    })
}
