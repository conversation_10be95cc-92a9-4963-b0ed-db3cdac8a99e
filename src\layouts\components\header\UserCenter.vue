<script setup lang="ts">
import IconLogout from "~icons/icon-park-outline/logout"
import IconUser from "~icons/icon-park-outline/user"
import { useAuthStore } from "@/store"

const { t } = useI18n()

const dialog = useDialog()

const { userInfo, logout } = useAuthStore()
const router = useRouter()

const options = computed(() => {
  return [
    {
      label: t("app.userCenter"),
      key: "userCenter",
      icon: () => h(IconUser),
    },
    {
      type: "divider",
      key: "d1",
    },
    {
      label: t("app.loginOut"),
      key: "loginOut",
      icon: () => h(IconLogout),
    },
  ]
})
function handleSelect(key: string | number) {
  if (key === "loginOut") {
    dialog.create({
      title: t("app.loginOutTitle"),
      content: t("app.loginOutContent"),
      positiveText: t("common.confirm"),
      negativeText: t("common.cancel"),
      onPositiveClick: () => {
        logout()
      },
    })
  }
  if (key === "userCenter")
    router.push("/userCenter")
}
</script>

<template>
  <n-dropdown
    trigger="click"
    :options="options"
    @select="handleSelect"
  >
    <n-avatar
      round
      class="cursor-pointer"
      :src="userInfo?.avatar"
    >
      <template #fallback>
        <div class="flex h-full w-full items-center justify-center">
          <icon-park-outline-user />
        </div>
      </template>
    </n-avatar>
  </n-dropdown>
</template>

<style scoped></style>
