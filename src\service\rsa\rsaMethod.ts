import { AES, enc, mode, pad } from "crypto-js"
import { JSEncrypt } from "jsencrypt"

const publicKey = import.meta.env.VITE_PUBLICKEY

// 字符升序排序
function sortObjectByKeys(obj: any) {
  return Object.keys(obj)
    .sort()
    .reduce((sortedObj: any, key: any) => {
      sortedObj[key] = obj[key]
      return sortedObj
    }, {})
}

// 随机16位数字
export function Random16CharsFn() {
  let result = ""
  const characters = "abcdefghijklmnopqrstuvwxyz0123456789"
  const charactersLength = characters.length
  for (let i = 0; i < 16; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength))
  }
  return result.toString()
}

// 生成key
export function GenerateKeyFn(val: string) {
  const encryptStr = new JSEncrypt()
  encryptStr.setPublicKey(publicKey)
  return encryptStr.encrypt(val)
}

// 加密
export function DataEncodeFn(data: any, key: string, key16: string) {
  const keyUtf8 = enc.Utf8.parse(key16)
  const encrypted = AES.encrypt(JSON.stringify(sortObjectByKeys(data)), keyUtf8, {
    mode: mode.ECB,
    padding: pad.Pkcs7,
  }).toString()

  return {
    signType: "RSA",
    key,
    data: encrypted,
  }
}

// 解密
export async function DataDecodingFn(data: string, key16: string) {
  const keyUtf8 = enc.Utf8.parse(key16)
  const decryptedData = AES.decrypt(data, keyUtf8, {
    mode: mode.ECB,
    padding: pad.Pkcs7,
  })
  return decryptedData.toString(enc.Utf8)
}
