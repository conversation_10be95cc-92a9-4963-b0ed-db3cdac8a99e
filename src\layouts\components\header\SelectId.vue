<script setup lang="ts">
import type { SelectRenderLabel, SelectRenderTag } from "naive-ui"
import { NAvatar, NText } from "naive-ui"
import LogoDark from "@/assets/images/logo_dark.png"
import LogoLight from "@/assets/images/logo_light.png"

const merchantID = ref("")
const options = ref([
  {
    label: "Drive My Car",
    value: "song1",
  },
  {
    label: "Norwegian Wood",
    value: "song2",
  },
  {
    label: "Wait",
    value: "song12",
  },
])
const isDark = useDark()
const renderSingleSelectTag: SelectRenderTag = ({ option }) => {
  return h(
    "div",
    {
      style: {
        display: "flex",
        alignItems: "center",
      },
    },
    [
      h(NAvatar, {
        src: isDark ? LogoDark : LogoLight,
        round: true,
        size: 24,
        style: {
          marginRight: "12px",
        },
      }),
      option.label as string,
    ],
  )
}

const renderLabel: SelectRenderLabel = (option) => {
  return h(
    "div",
    {
      style: {
        display: "flex",
        alignItems: "center",
      },
    },
    [
      h(NAvatar, {
        src: isDark ? LogoDark : LogoLight,
        round: true,
        size: "small",
      }),
      h(
        "div",
        {
          style: {
            marginLeft: "12px",
            padding: "4px 0",
          },
        },
        [
          h("div", null, [option.label as string]),
          h(
            NText,
            { depth: 3, tag: "div" },
            {
              default: () => "description",
            },
          ),
        ],
      ),
    ],
  )
}

onMounted(() => {
  merchantID.value = options.value[0].value
})
</script>

<template>
  <n-select
    v-model:value="merchantID"
    :render-label="renderLabel" :render-tag="renderSingleSelectTag" :options="options" bordered placeholder="商家ID"
  >
    <template #header>
      header
    </template>
  </n-select>
</template>
