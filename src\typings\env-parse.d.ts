/// <reference types="vite/client" />
interface ImportMetaEnv {
  // Auto generate by env-parse
  /**
   * 项目名称
   */
  readonly VITE_APP_NAME: string
  /**
   * 路由加载模式 static ｜ dynamic
   */
  readonly VITE_ROUTE_LOAD_MODE: string
  /**
   * 设置登陆后跳转地址
   * VITE_HOME_PATH=/dashboard/workbench
   */
  readonly VITE_HOME_PATH: string
  /**
   * 本地存储前缀
   */
  readonly VITE_STORAGE_PREFIX: string
  /**
   * 版权信息
   */
  readonly VITE_COPYRIGHT_INFO: string
  /**
   * 默认多语言 enUS | zhCN
   */
  readonly VITE_DEFAULT_LANG: string
  /**
   * build时是否关闭控制台打印
   */
  readonly VITE_DEL_CONSOLE: boolean
  /**
   * 路径基地址
   */
  readonly VITE_BASE_URL: string
  /**
   * 接口基地址
   */
  readonly VITE_BASE_API: string
  /**
   * dev public key
   */
  readonly VITE_PUBLICKEY: string
}