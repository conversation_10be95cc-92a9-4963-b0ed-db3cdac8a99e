import type { App } from "vue"
import messages from "@intlify/unplugin-vue-i18n/messages"
import { createI18n } from "vue-i18n"
// import { local } from "@/utils"
// import enUS from "~/locales/enUS.json"
// import zhCN from "~/locales/zhCN.json"

const { VITE_DEFAULT_LANG } = import.meta.env
// console.log("messages", messages)
export const i18n = createI18n({
  legacy: false,
  globalInjection: true,
  locale: VITE_DEFAULT_LANG, // 默认显示语言
  fallbackLocale: VITE_DEFAULT_LANG,
  messages,
  // messages: {
  //   zhCN,
  //   enUS,
  // },
  // 缺失国际化键警告
  // missingWarn: false,

  // 缺失回退内容警告
  fallbackWarn: false,
})

export function install(app: App) {
  app.use(i18n)
}
