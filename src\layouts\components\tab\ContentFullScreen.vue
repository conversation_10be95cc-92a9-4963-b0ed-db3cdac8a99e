<script setup lang="ts">
import { useAppStore } from "@/store"

const appStore = useAppStore()

let previousLayoutMode = appStore.layoutMode

function enterFullContent() {
  previousLayoutMode = appStore.layoutMode
  appStore.layoutMode = "full-content"
}

function exitFullContent() {
  // 如果是全屏或者数据不存在，则恢复为默认的vertical
  if (previousLayoutMode === "full-content" || !previousLayoutMode) {
    previousLayoutMode = "vertical"
  }
  appStore.layoutMode = previousLayoutMode
}
</script>

<template>
  <n-tooltip v-if="!appStore.isMobile" placement="bottom" trigger="hover">
    <template #trigger>
      <CommonWrapper @click="enterFullContent">
        <icon-park-outline-full-screen-one />
      </CommonWrapper>
    </template>
    {{ $t('app.togglContentFullScreen') }}
  </n-tooltip>

  <Teleport to="body">
    <div
      v-if="appStore.layoutMode === 'full-content'"
      class="right-0 top-4 fixed z-[9999]"
    >
      <n-tooltip placement="left" trigger="hover">
        <template #trigger>
          <n-el
            class="c-[var(--base-color)] p-2 rounded-l-lg bg-[var(--primary-color)] cursor-pointer shadow-lg"
            @click="exitFullContent"
          >
            <icon-park-outline-off-screen-one />
          </n-el>
        </template>
        {{ $t('app.togglContentFullScreen') }}
      </n-tooltip>
    </div>
  </Teleport>
</template>
