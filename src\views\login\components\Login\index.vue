<script setup lang="ts">
import type { FormInst } from "naive-ui"

import { adminCaptcha } from "@/service"
import { useAuthStore } from "@/store"
import { local } from "@/utils"

const emit = defineEmits(["update:modelValue"])

const authStore = useAuthStore()

function toOtherForm(type: any) {
  emit("update:modelValue", type)
}

const { t } = useI18n()
const rules = computed(() => {
  return {
    account: {
      required: true,
      trigger: "blur",
      message: t("login.accountRuleTip"),
    },
    password: {
      required: true,
      trigger: "blur",
      message: t("login.passwordRuleTip"),
    },
    captcha: {
      required: true,
      trigger: "blur",
      message: t("login.captchaRuleTip"),
    },
  }
})
const captchaImg = ref("")
const captchaKey = ref("")
const captchaStatus = ref(false)

async function getCaptcha() {
  captchaStatus.value = false
  captchaImg.value = ""
  captchaKey.value = ""

  const { data, isSuccess } = await adminCaptcha()

  if (isSuccess) {
    captchaKey.value = data.key
    captchaStatus.value = isSuccess
    captchaImg.value = data.img
  }
}

const formValue = ref({
  account: "admin",
  password: "654321",
  captcha: "",
})
const isRemember = ref(false)
const isLoading = ref(false)

const formRef = ref<FormInst | null>(null)
function handleLogin() {
  formRef.value?.validate(async (errors) => {
    if (errors)
      return

    isLoading.value = true
    const { account, password, captcha } = formValue.value

    if (isRemember.value)
      local.set("loginAccount", { account, password })
    else local.remove("loginAccount")

    await authStore.login({
      account,
      password,
      captcha,
      key: captchaKey.value,
    }, () => {
      getCaptcha()
    })

    isLoading.value = false
  })
}
onMounted(() => {
  getCaptcha()
  checkUserAccount()
})
function checkUserAccount() {
  const loginAccount = local.get("loginAccount")
  if (!loginAccount)
    return

  formValue.value = loginAccount
  isRemember.value = true
}
</script>

<template>
  <div>
    <n-h2 depth="3" class="text-center">
      {{ $t('login.signInTitle') }}
    </n-h2>
    <n-form ref="formRef" :rules="rules" :model="formValue" :show-label="false" size="large">
      <n-form-item path="account">
        <n-input v-model:value="formValue.account" clearable :placeholder="$t('login.accountPlaceholder')" />
      </n-form-item>
      <n-form-item path="password">
        <n-input v-model:value="formValue.password" type="password" :placeholder="$t('login.passwordPlaceholder')" clearable show-password-on="click">
          <template #password-invisible-icon>
            <icon-park-outline-preview-close-one />
          </template>
          <template #password-visible-icon>
            <icon-park-outline-preview-open />
          </template>
        </n-input>
      </n-form-item>

      <n-form-item
        path="captcha"
      >
        <n-skeleton v-if="!captchaStatus" class="!h-40px !w-150px" :sharp="false" size="large" />

        <n-image
          v-else
          class="!h-40px !w-150px !rounded-3px"
          preview-disabled
          :src="captchaImg"
          @click="getCaptcha"
        />

        <n-input
          v-model:value="formValue.captcha"
          clearable :placeholder="$t('login.captchaPlaceholder')" class="ml-10px" @keyup.enter="handleLogin"
        />
      </n-form-item>
      <n-space vertical :size="20" class="pb-26px">
        <div class="flex items-center justify-between">
          <n-checkbox v-model:checked="isRemember">
            {{ $t('login.rememberMe') }}
          </n-checkbox>
          <n-button type="primary" text @click="toOtherForm('resetPwd')">
            {{ $t('login.forgotPassword') }}
          </n-button>
        </div>
        <n-button block type="primary" size="large" :loading="isLoading" :disabled="isLoading" @click="handleLogin">
          {{ $t('login.signIn') }}
        </n-button>
        <!-- <n-flex class="pb-10">
          <n-text>{{ $t('login.noAccountText') }}</n-text>
          <n-button type="primary" text @click="toOtherForm('register')">
            {{ $t('login.signUp') }}
          </n-button>
        </n-flex> -->
      </n-space>
    </n-form>
  </div>
</template>

<style scoped></style>
