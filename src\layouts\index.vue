<script setup lang="ts">
import type { MenuOption } from "naive-ui"
import type { ProLayoutMode } from "pro-naive-ui"

import { ProLayout, useLayoutMenu } from "pro-naive-ui"
import { useAppStore, useRouteStore } from "@/store"
import {
  BackTop,
  CollapaseButton,
  FullScreen,
  Logo,
  MobileDrawer,
  SelectId,
  Setting,
  SettingDrawer,
  TabBar,
  UserCenter,
} from "./components"
import Content from "./Content.vue"

const route = useRoute()
const appStore = useAppStore()
const routeStore = useRouteStore()

const { layoutMode } = storeToRefs(useAppStore())

const {
  layout,
  activeKey,
} = useLayoutMenu({
  mode: appStore.layoutMode as ProLayoutMode,
  menus: routeStore.menus as MenuOption[],
})

watch(() => route.path, (value: string) => {
  activeKey.value = value
}, { immediate: true })

// 移动端抽屉控制
const showMobileDrawer = ref(false)

const sidebarWidth = ref(240)
const sidebarCollapsedWidth = ref(64)

const hasHorizontalMenu = computed(() => ["horizontal", "mixed-two-column", "mixed-sidebar"].includes(layoutMode.value))

const hidenCollapaseButton = computed(() => ["horizontal"].includes(layoutMode.value) || appStore.isMobile)
</script>

<template>
  <!-- <SettingDrawer /> -->
  <ProLayout
    v-model:collapsed="appStore.collapsed"
    :mode="layoutMode"
    :is-mobile="appStore.isMobile"
    :show-logo="appStore.showLogo && !appStore.isMobile"
    :show-footer="appStore.showFooter"
    :show-tabbar="appStore.showTabs"
    nav-fixed
    show-nav
    show-sidebar
    :nav-height="60"
    :tabbar-height="45"
    :footer-height="40"
    :sidebar-width="sidebarWidth"
    :sidebar-collapsed-width="sidebarCollapsedWidth"
    has-sider
  >
    <template #default>
      <Content />
      <BackTop />
      <SettingDrawer />

      <!-- 移动端功能抽屉 -->
      <MobileDrawer v-model:show="showMobileDrawer">
        <n-menu v-bind="layout.verticalMenuProps" />
      </MobileDrawer>
    </template>
    <template #footer>
      <div class="h-full flex items-center justify-center">
        {{ appStore.footerText }}
      </div>
    </template>
    <template #logo>
      <Logo />
    </template>
    <template #nav-center>
      <div class="h-full flex items-center gap-1">
        <n-menu v-if="hasHorizontalMenu" v-bind="layout.horizontalMenuProps" />
      </div>
    </template>
    <template #nav-left>
      <template v-if="appStore.isMobile">
        <Logo />
      </template>

      <template v-else>
        <div v-if="!hasHorizontalMenu || !hidenCollapaseButton" class="h-full flex items-center gap-1 p-x-sm">
          <CollapaseButton v-if="!hidenCollapaseButton" />
          <SelectId class="w-380px px-10px" />
        </div>
      </template>
    </template>
    <template #nav-right>
      <div class="h-full flex items-center gap-1 p-x-xl">
        <!-- 移动端：只显示菜单按钮 -->
        <template v-if="appStore.isMobile">
          <n-button
            quaternary
            @click="showMobileDrawer = true"
          >
            <template #icon>
              <n-icon size="18">
                <icon-park-outline-hamburger-button />
              </n-icon>
            </template>
          </n-button>
        </template>

        <!-- 桌面端：显示完整功能组件 -->
        <template v-else>
          <!-- <Search /> -->
          <!-- <Notices /> -->
          <FullScreen />
          <DarkModeSwitch />
          <LangsSwitch />
          <Setting />
          <UserCenter />
        </template>
      </div>
    </template>
    <template #sidebar>
      <n-scrollbar class="flex-[1_0_0]">
        <n-menu v-bind="layout.verticalMenuProps" :collapsed-width="sidebarCollapsedWidth" />
      </n-scrollbar>
    </template>
    <template #tabbar>
      <TabBar />
    </template>
  </ProLayout>
</template>
