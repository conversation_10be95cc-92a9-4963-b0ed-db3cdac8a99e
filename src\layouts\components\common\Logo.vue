<script setup lang="ts">
import LogoDark from "@/assets/images/logo_dark.png"
import LogoLight from "@/assets/images/logo_light.png"
import { useAppStore } from "@/store"

const isDark = useDark()
const router = useRouter()
const appStore = useAppStore()

const name = import.meta.env.VITE_APP_NAME

const hidenLogoText = computed(() => {
  if (["sidebar", "mixed-sidebar", "horizontal"].includes(appStore.layoutMode)) {
    return false
  }
  if (["two-column", "mixed-two-column"].includes(appStore.layoutMode)) {
    return true
  }
  return appStore.collapsed
})
</script>

<template>
  <div
    class="text-xl p-x-2 flex gap-2 h-60px cursor-pointer items-center justify-center"
    @click="router.push('/')"
  >
    <!-- <svg-icons-logo class="text-1.5em" /> -->
    <img :src="isDark ? LogoDark : LogoLight" class="size-1.5em" alt="logo">
    <span
      v-show="!hidenLogoText"
      class="whitespace-nowrap text-ellipsis overflow-hidden"
    >{{ name }}</span>
  </div>
</template>

<style scoped></style>
